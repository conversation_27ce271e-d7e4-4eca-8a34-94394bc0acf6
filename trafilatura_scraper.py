"""
Enhanced Trafilatura-based web content extractor
Optimized for Channel News Asia and other news websites
"""
import trafilatura
from trafilatura.settings import Extractor
from trafilatura import extract_metadata, bare_extraction
from playwright.sync_api import sync_playwright
from playwright_stealth import stealth_sync
import json
import time
from typing import Dict, Any, Optional

class NewsContentExtractor:
    """Enhanced news content extractor using Trafilatura"""

    def __init__(self):
        # Default extraction parameters optimized for news content
        self.extraction_params = {
            'output_format': 'json',
            'with_metadata': True,
            'include_comments': False,  # Skip comments for cleaner output
            'include_tables': True,     # Keep tables for structured data
            'include_links': True,      # Keep links for references
            'include_formatting': True, # Preserve formatting
            'favor_recall': True,       # Get more content when unsure
            'target_language': 'en'     # Focus on English content
        }

    def get_html_with_playwright(self, url: str) -> Optional[str]:
        """Get rendered HTML using Playwright with stealth mode"""
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(
                    headless=True,
                    args=['--no-sandbox', '--disable-dev-shm-usage']
                )
                context = browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                )
                page = context.new_page()

                # Apply stealth mode to the page
                stealth_sync(page)

                # Navigate to URL with timeout
                page.goto(url, wait_until='domcontentloaded', timeout=60000)

                # Wait a bit for dynamic content
                time.sleep(3)

                # Get the HTML content
                html_content = page.content()
                browser.close()

                return html_content

        except Exception as e:
            print(f"Error fetching HTML with Playwright: {e}")
            return None

    def extract_with_fallback(self, html_content: str, url: str) -> Dict[str, Any]:
        """Extract content with multiple fallback methods"""
        results = {}

        # Method 1: Full extraction with metadata
        try:
            result = trafilatura.extract(
                html_content,
                url=url,
                **self.extraction_params
            )

            if result:
                results['full_extraction'] = json.loads(result)

        except Exception as e:
            print(f"Full extraction failed: {e}")

        # Method 2: Bare extraction for more control
        try:
            # Create parameters for bare_extraction (exclude output_format)
            bare_params = {k: v for k, v in self.extraction_params.items() if k != 'output_format'}
            bare_result = bare_extraction(
                html_content,
                url=url,
                **bare_params
            )

            if bare_result:
                results['bare_extraction'] = bare_result.as_dict()

        except Exception as e:
            print(f"Bare extraction failed: {e}")

        # Method 3: Metadata only
        try:
            metadata = extract_metadata(html_content, default_url=url)
            if metadata:
                results['metadata_only'] = metadata.as_dict()

        except Exception as e:
            print(f"Metadata extraction failed: {e}")

        # Method 4: Baseline extraction as last resort
        try:
            from trafilatura import baseline
            _, text, length = baseline(html_content)
            if text and length > 100:  # Only if substantial content
                results['baseline'] = {
                    'text': text,
                    'length': length,
                    'url': url
                }
        except Exception as e:
            print(f"Baseline extraction failed: {e}")

        return results

    def scrape_url(self, url: str) -> Dict[str, Any]:
        """Main scraping function"""
        print(f"Scraping: {url}")

        # Get HTML content
        html_content = self.get_html_with_playwright(url)
        if not html_content:
            return {"error": "Failed to fetch HTML content"}

        # Extract content with multiple methods
        results = self.extract_with_fallback(html_content, url)

        if not results:
            return {"error": "All extraction methods failed"}

        return results

    def get_best_result(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Get the best extraction result"""
        # Priority order: full_extraction > bare_extraction > metadata_only > baseline
        for method in ['full_extraction', 'bare_extraction', 'metadata_only', 'baseline']:
            if method in results and results[method]:
                return {
                    'method_used': method,
                    'content': results[method]
                }

        return {"error": "No valid extraction found"}

def main():
    """Main function for command line usage"""
    extractor = NewsContentExtractor()

    url = input("Enter URL to scrape: ").strip()
    if not url:
        print("No URL provided")
        return

    # Extract content
    results = extractor.scrape_url(url)

    if "error" in results:
        print(f"\nError: {results['error']}")
        return

    # Get the best result
    best_result = extractor.get_best_result(results)

    if "error" in best_result:
        print(f"\nError: {best_result['error']}")
        return

    print(f"\n=== EXTRACTION SUCCESSFUL ===")
    print(f"Method used: {best_result['method_used']}")
    print(f"\n=== CONTENT ===")

    content = best_result['content']

    # Display based on content type
    if isinstance(content, dict):
        # JSON-like content
        if 'title' in content:
            print(f"Title: {content['title']}")
        if 'author' in content:
            print(f"Author: {content['author']}")
        if 'date' in content:
            print(f"Date: {content['date']}")
        if 'url' in content:
            print(f"URL: {content['url']}")

        text_content = content.get('text', content.get('raw_text', ''))
        if text_content:
            print(f"\nText ({len(text_content)} chars):")
            print(text_content[:2000] + ("..." if len(text_content) > 2000 else ""))
    else:
        # Plain text content
        print(str(content)[:2000] + ("..." if len(str(content)) > 2000 else ""))

if __name__ == "__main__":
    main()
