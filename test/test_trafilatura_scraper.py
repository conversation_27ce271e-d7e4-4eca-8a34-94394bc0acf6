"""
Test script for trafilatura scraper with specific URLs
"""
import json
import time
import sys
import os

# Add parent directory to path to import trafilatura_scraper
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from trafilatura_scraper import NewsContentExtractor

def test_url(extractor, url, test_name):
    """Test a single URL and display results"""
    print(f"\n{'='*80}")
    print(f"Testing {test_name}")
    print(f"URL: {url}")
    print(f"{'='*80}")

    start_time = time.time()

    try:
        # Extract content
        results = extractor.scrape_url(url)

        end_time = time.time()
        print(f"Extraction time: {end_time - start_time:.2f} seconds")

        if "error" in results:
            print(f"❌ Error: {results['error']}")
            return False

        # Display all extraction methods that worked
        print(f"\n📊 Extraction Methods Results:")
        for method, content in results.items():
            if content:
                print(f"  ✅ {method}: Success")
            else:
                print(f"  ❌ {method}: Failed")

        # Get the best result
        best_result = extractor.get_best_result(results)

        if "error" in best_result:
            print(f"❌ Best result error: {best_result['error']}")
            return False

        print(f"\n🏆 Best Method: {best_result['method_used']}")

        # Display content details
        content = best_result['content']
        print(f"\n📄 Content Details:")

        if isinstance(content, dict):
            # Display metadata
            metadata_fields = ['title', 'author', 'date', 'url', 'sitename', 'description']
            for field in metadata_fields:
                if field in content and content[field]:
                    print(f"  {field.capitalize()}: {content[field]}")

            # Display text content info
            text_content = content.get('text', content.get('raw_text', ''))
            if text_content:
                print(f"  Text length: {len(text_content)} characters")
                print(f"  Text preview: {text_content[:200]}...")
            else:
                print("  ⚠️ No text content found")

            # Display other fields
            other_fields = ['tags', 'categories', 'language']
            for field in other_fields:
                if field in content and content[field]:
                    print(f"  {field.capitalize()}: {content[field]}")

        else:
            # Plain text content
            print(f"  Content type: {type(content)}")
            print(f"  Content length: {len(str(content))} characters")
            print(f"  Content preview: {str(content)[:200]}...")

        print(f"✅ {test_name} - SUCCESS")
        return True

    except Exception as e:
        end_time = time.time()
        print(f"Extraction time: {end_time - start_time:.2f} seconds")
        print(f"❌ Exception occurred: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Trafilatura Scraper")
    print("=" * 80)

    # Initialize extractor
    extractor = NewsContentExtractor()

    # Test URLs
    test_urls = [
        {
            "url": "https://www.capitaland.com/en/about-capitaland/newsroom/news-releases/international/2025/june/ESG-CLI-launch-retail-maverick-challenge.html",
            "name": "CapitaLand News Article"
        },
        {
            "url": "https://www.businesstimes.com.sg/",
            "name": "Business Times Homepage"
        }
    ]

    results = []

    for test_case in test_urls:
        success = test_url(extractor, test_case["url"], test_case["name"])
        results.append({
            "name": test_case["name"],
            "url": test_case["url"],
            "success": success
        })

    # Summary
    print(f"\n{'='*80}")
    print("📋 TEST SUMMARY")
    print(f"{'='*80}")

    successful_tests = sum(1 for r in results if r["success"])
    total_tests = len(results)

    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"{status} - {result['name']}")

    print(f"\nOverall: {successful_tests}/{total_tests} tests passed")

    if successful_tests == total_tests:
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
