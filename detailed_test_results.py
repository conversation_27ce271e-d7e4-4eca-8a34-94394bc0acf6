"""
Detailed test to show full extraction results for the two URLs
"""
import json
from trafilatura_scraper import NewsContentExtractor

def detailed_test():
    """Run detailed test and save results"""
    extractor = NewsContentExtractor()
    
    urls = [
        "https://www.capitaland.com/en/about-capitaland/newsroom/news-releases/international/2025/june/ESG-CLI-launch-retail-maverick-challenge.html",
        "https://www.businesstimes.com.sg/"
    ]
    
    for i, url in enumerate(urls, 1):
        print(f"\n{'='*100}")
        print(f"DETAILED EXTRACTION RESULTS - URL {i}")
        print(f"URL: {url}")
        print(f"{'='*100}")
        
        # Extract content
        results = extractor.scrape_url(url)
        
        if "error" in results:
            print(f"❌ Error: {results['error']}")
            continue
        
        # Get best result
        best_result = extractor.get_best_result(results)
        content = best_result['content']
        
        print(f"\n🏆 Best extraction method: {best_result['method_used']}")
        
        if isinstance(content, dict):
            print(f"\n📋 METADATA:")
            metadata_fields = ['title', 'author', 'date', 'url', 'sitename', 'description', 'tags', 'categories', 'language']
            for field in metadata_fields:
                if field in content and content[field]:
                    print(f"  {field}: {content[field]}")
            
            print(f"\n📄 FULL TEXT CONTENT:")
            text_content = content.get('text', content.get('raw_text', ''))
            if text_content:
                print(f"Length: {len(text_content)} characters")
                print(f"Content:\n{text_content}")
            else:
                print("No text content found")
                
            # Save to file for inspection
            filename = f"extraction_result_{i}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(content, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Full result saved to: {filename}")
        
        print(f"\n{'='*50}")

if __name__ == "__main__":
    detailed_test()
