# Singapore News Intelligence Chatbot

## Project Vision
An AI-powered conversational interface that aggregates, processes, and personalizes Singapore news content. The system intelligently curates local news based on user-defined relevance criteria and delivers insights through natural conversation.

## System Architecture

### Technology Stack
- **Frontend**: Next.js 14, React 18, Tai<PERSON>wind CSS
- **Backend**: Python 3.11, FastAPI, Uvicorn
- **Database**: SQLite with WAL mode
- **AI/ML**: Google Gemini 2.0 Lite API
- **Scraping**: Newspaper3k, Feedparser, Requests
- **Deployment**: Railway (containerized)
- **Process Management**: APScheduler for background tasks

### Architecture Pattern
```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────┐
│   Next.js   │───▶│   FastAPI    │───▶│   SQLite    │    │   Gemini    │
│  Frontend   │    │   REST API   │    │  Database   │    │     AI      │
└─────────────┘    └──────────────┘    └─────────────┘    └─────────────┘
                           │                                       ▲
                           ▼                                       │
                   ┌──────────────┐                              │
                   │  Background  │──────────────────────────────┘
                   │   Scraper    │
                   └──────────────┘
```

## Core Components

### 1. Data Ingestion Pipeline
**Purpose**: Automated collection and processing of Singapore news content

**Implementation Strategy**:
- **Primary Sources (RSS)**: Channel NewsAsia, TODAY, Business Times, Yahoo Singapore
- **Secondary Sources (Newspaper3k)**: Mothership, Rice Media, AsiaOne, The New Paper
- **Processing Frequency**: Hourly batch jobs with intelligent rate limiting
- **Content Extraction**: Newspaper3k for automatic article parsing and metadata
- **Deduplication**: Content hashing and URL canonicalization

**Error Resilience**:
```python
from newspaper import Article
import trafilatura  # Fallback

def extract_article(url):
    try:
        article = Article(url)
        article.download()
        article.parse()
        return {
            'title': article.title,
            'text': article.text,
            'publish_date': article.publish_date
        }
    except Exception:
        # Fallback to trafilatura for JS-heavy sites
        content = trafilatura.extract(trafilatura.fetch_url(url))
        return {'text': content, 'title': None, 'publish_date': None}
```

### 2. Content Intelligence Layer
**AI Processing Pipeline**:
1. **Content Summarization**: Gemini 2.0 Lite generates concise summaries
2. **Relevance Scoring**: User-defined prompts determine content importance
3. **Batch Optimization**: Multiple articles processed per API call
4. **Cost Management**: Intelligent caching and summary reuse

**Relevance Engine**:
```python
def calculate_relevance(article, user_context):
    prompt = f"""
    User Context: {user_context}
    Article: {article.title} - {article.summary}
    
    Rate relevance (0-10) and explain why this matters to the user.
    """
    return gemini_api.score_relevance(prompt)
```

### 3. Conversational Interface
**Chat Features**:
- **Persistent Memory**: Conversation context maintained across sessions
- **Multi-modal Responses**: Text summaries + article previews + direct links
- **Follow-up Handling**: Natural conversation flow with article references
- **Real-time Updates**: Live article suggestions during chat

**API Endpoints**:
```
POST /api/chat/message    - Send message, receive AI response
GET  /api/articles/recent - Fetch latest processed articles
PUT  /api/settings/context - Update user relevance criteria
GET  /api/health         - System status and metrics
```

## Database Schema

```sql
-- Core article storage
CREATE TABLE articles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    summary TEXT,
    url TEXT UNIQUE NOT NULL,
    source TEXT NOT NULL,
    published_at DATETIME,
    processed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    relevance_score REAL DEFAULT 0,
    content_hash TEXT UNIQUE,
    INDEX(published_at),
    INDEX(source),
    INDEX(relevance_score)
);

-- Conversation management
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id TEXT UNIQUE,
    user_message TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    context_summary TEXT,
    referenced_articles TEXT, -- JSON array of article IDs
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX(timestamp)
);

-- User configuration
CREATE TABLE user_settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- System monitoring
CREATE TABLE scraping_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source TEXT NOT NULL,
    status TEXT NOT NULL, -- success/failure/partial
    articles_found INTEGER DEFAULT 0,
    errors TEXT,
    execution_time REAL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Development Phases

### Phase 1: Foundation (Weeks 1-3)
**Data Pipeline Development**
- RSS feed integration for reliable sources
- Web scraping infrastructure with fallback mechanisms
- SQLite database setup with optimization
- Basic Gemini API integration

**Deliverables**:
- Functional scraper for 5+ Singapore news sources
- Database populated with daily article summaries
- Error handling and monitoring systems

### Phase 2: Intelligence Layer (Weeks 4-5)
**AI Integration & Processing**
- Relevance scoring implementation
- Conversation memory system
- Batch processing optimization
- Cost monitoring and controls

**Deliverables**:
- Personalized article ranking
- Conversation context management
- API cost tracking dashboard

### Phase 3: User Interface (Weeks 6-7)
**Frontend Development**
- Responsive chat interface
- Article preview components
- Settings management UI
- Real-time message streaming

**Deliverables**:
- Production-ready web application
- Mobile-responsive design
- User preference configuration

### Phase 4: Production Deployment (Weeks 8-10)
**Optimization & Monitoring**
- Performance tuning
- Error monitoring
- Analytics implementation
- Documentation completion

## Risk Management

### Technical Risks
| Risk | Impact | Mitigation |
|------|--------|------------|
| Source website changes | High | RSS-first strategy, multiple selectors |
| API rate limits | Medium | Intelligent batching, exponential backoff |
| Deployment complexity | Low | Containerized deployment, Railway platform |

### Cost Management
- **Target**: <$30/month operational cost
- **Monitoring**: Real-time API usage tracking
- **Controls**: Request batching, response caching, usage alerts

### Scalability Considerations
- **Database**: SQLite → PostgreSQL migration path
- **Deployment**: Single container → microservices architecture
- **Processing**: Synchronous → async task queues

## Success Metrics

### Technical KPIs
- **Uptime**: >99% availability
- **Performance**: <3s response time
- **Coverage**: 50+ articles processed daily
- **Accuracy**: >90% successful scraping rate

### User Experience
- **Relevance**: User satisfaction with article recommendations
- **Engagement**: Daily conversation frequency
- **Utility**: Click-through rate on article links

## Development Environment

### Prerequisites
```bash
# Python environment
python 3.11+
pip install -r requirements.txt

# Node.js environment  
node 18+
npm install

# Environment variables
GEMINI_API_KEY=your_key_here
DATABASE_URL=sqlite:///news.db
```

### Local Development
```bash
# Backend
cd backend
uvicorn main:app --reload --port 8000

# Frontend
cd frontend  
npm run dev
```

### Deployment
```bash
# Railway deployment
railway login
railway init
railway up
```

This architecture balances development speed, cost efficiency, and technical robustness while maintaining clear separation of concerns and scalability pathways.